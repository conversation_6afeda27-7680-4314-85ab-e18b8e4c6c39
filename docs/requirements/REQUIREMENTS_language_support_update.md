# 本地文件夹Provider语言支持功能更新

## 更新概述

根据本地文件夹provider的实际语言支持情况，对语言匹配逻辑进行了具体优化，实现了provider特定的语言支持规则，并使用标准库重构了语言代码标准化功能。

## 主要改进

1. **Provider特定规则**：为不同的本地文件夹provider实现了专门的语言匹配规则
2. **标准化重构**：使用`golang.org/x/text/language`标准库替代硬编码的语言转换
3. **通用兼容性**：支持各种语言代码格式的自动标准化
4. **向后兼容**：保持现有功能不受影响

## 更新内容

### 1. Provider特定语言支持规则

#### 1.1 Google本地文件夹 (ProviderGoogleLocalDirectory)

- **支持**：多语言匹配
- **匹配逻辑**：标准语言代码匹配
- **行为**：token.Language与mapReq.Lang进行标准化后精确匹配

#### 1.2 天地图本地文件夹 (ProviderTiandituLocalDirectory)

- **支持**：仅支持简体中文
- **匹配逻辑**：只接受中文语言代码
- **行为**：
  - 如果token.Language为空：始终匹配（向后兼容）
  - 如果token.Language为"zh-CN"且mapReq.Lang也为"zh-CN"：匹配
  - 其他情况：不匹配

#### 1.3 OSM本地文件夹 (ProviderOSMLocalDirectory)

- **支持**：无语言选项
- **匹配逻辑**：跳过所有语言匹配
- **行为**：语言根据地理位置/国家确定，始终返回true

### 2. 语言代码标准化

#### 2.1 标准化函数

重构`normalizeLanguageCode`函数，使用`golang.org/x/text/language`标准库：

```go
func normalizeLanguageCode(lang string) string {
    if lang == "" {
        return ""
    }

    // Use language.All.Parse for maximum compatibility (same as parseMapReq)
    langTag, err := language.All.Parse(lang)
    if err != nil {
        // Return original string for backward compatibility
        return lang
    }

    // Convert to standard BCP 47 format
    return langTag.String()
}
```

#### 2.2 标准化特性

- **通用性**：使用`language.All.Parse`支持各种语言代码格式
- **标准化**：自动转换为BCP 47标准格式
- **兼容性**：与`parseMapReq`函数使用相同的解析器
- **容错性**：无效语言代码返回原始字符串，保持向后兼容

#### 2.3 支持的格式示例

- `zh_CN` → `zh-CN`
- `en_US` → `en-US`
- `zh` → `zh`
- `fr_FR` → `fr-FR`
- `invalid-code` → `invalid-code`（保持原样）

### 3. 更新的语言匹配逻辑

#### 3.1 卫星图类型

- **所有provider**：跳过语言匹配（卫星图无语言差别）

#### 3.2 路线图和混合图类型

- **Google**：标准多语言匹配
- **天地图**：仅中文匹配
- **OSM**：跳过语言匹配

#### 3.3 向后兼容性

- **空Language字段**：在所有provider中都作为默认/fallback，始终匹配
- **现有token**：不受影响，继续正常工作

## 实现细节

### 修改的文件

- `maps/maps.go`：更新`isLanguageMatched`函数，新增`normalizeLanguageCode`函数

### 核心逻辑变更

1. **Provider类型检查**：根据不同的本地文件夹provider类型应用不同规则
2. **语言标准化**：在匹配前对语言代码进行标准化处理
3. **特殊处理**：为天地图添加中文专用逻辑，为OSM跳过语言检查

## 测试场景

### 1. Google本地文件夹

- ✅ token.Language="en", mapReq.Lang="en" → 匹配
- ✅ token.Language="zh-CN", mapReq.Lang="zh" → 匹配（标准化后）
- ❌ token.Language="en", mapReq.Lang="zh-CN" → 不匹配

### 2. 天地图本地文件夹

- ✅ token.Language="", mapReq.Lang="zh-CN" → 匹配（空token）
- ✅ token.Language="zh-CN", mapReq.Lang="zh" → 匹配（标准化后）
- ❌ token.Language="zh-CN", mapReq.Lang="en" → 不匹配

### 3. OSM本地文件夹

- ✅ token.Language="", mapReq.Lang="en" → 匹配（跳过检查）
- ✅ token.Language="zh-CN", mapReq.Lang="en" → 匹配（跳过检查）
- ✅ token.Language="en", mapReq.Lang="zh-CN" → 匹配（跳过检查）

### 4. 卫星图类型

- ✅ 所有provider和语言组合 → 匹配（跳过语言检查）

## 优势

1. **精确匹配**：根据每个provider的实际语言支持能力进行匹配
2. **标准化处理**：使用`golang.org/x/text/language`标准库，支持BCP 47格式转换
3. **通用兼容**：与现有`parseMapReq`函数使用相同的语言解析器，确保一致性
4. **向后兼容**：保持现有token和配置的正常工作，无效语言代码保持原样
5. **性能优化**：OSM provider跳过不必要的语言检查
6. **用户体验**：天地图用户无需担心语言设置，OSM用户获得地理位置相关的语言
7. **可扩展性**：标准库支持各种语言代码格式，无需硬编码特定语言规则

## 注意事项

1. **天地图限制**：只支持中文，非中文请求将无法匹配天地图本地文件夹token
2. **OSM特性**：语言由地理位置决定，不受token语言设置影响
3. **Google灵活性**：支持多语言，需要精确的语言代码匹配
4. **标准化依赖**：依赖`golang.org/x/text/language`标准库，确保与系统其他部分一致
5. **错误处理**：无效语言代码会保持原样，不会导致系统错误

## 后续扩展

1. **地区代码支持**：可以考虑添加地区代码的更精细匹配逻辑
2. **配置化规则**：可以将provider语言支持规则配置化，便于维护和扩展
3. **语言回退机制**：可以实现语言回退逻辑，如zh-CN回退到zh，en-US回退到en
4. **性能优化**：可以考虑缓存语言解析结果，避免重复解析相同的语言代码
